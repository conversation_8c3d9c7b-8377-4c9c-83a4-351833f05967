{"name": "@fluentui/react-icons", "version": "2.0.301", "sideEffects": false, "main": "lib-cjs/index.js", "module": "lib/index.js", "typings": "lib/index.d.ts", "description": "Fluent System Icons are a collection of familiar, friendly, and modern icons from Microsoft.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/microsoft/fluentui-system-icons.git"}, "scripts": {"clean": "find ./src -type f ! -name \"wrapIcon.tsx\" -name \"*.tsx\" -delete", "clean:svg": "rm -rf ./intermediate", "clean:gitignore": "git clean -fdX", "copy": "node ../../importer/generate.js --source=../../assets --dest=./intermediate --extension=svg --target=react", "copy:font-files": "cpy './src/utils/fonts/*.{ttf,woff,woff2,json}' ./lib/utils/fonts/. && cpy './src/utils/fonts/*.{ttf,woff,woff2,json}' ./lib-cjs/utils/fonts/.", "convert:svg": "node convert.js --source=./intermediate --dest=./src --rtl=./intermediate/rtl.json", "convert:fonts": "node convert-font.js --source=./src/utils/fonts --dest=./src/fonts --codepointDest=./src/utils/fonts --rtl=./intermediate/rtl.json", "generate:font-regular": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Regular --codepoints=../../fonts/FluentSystemIcons-Regular.json", "generate:font-filled": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Filled --codepoints=../../fonts/FluentSystemIcons-Filled.json", "generate:font-light": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Light", "generate:font-resizable": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Resizable", "generate:font": "npm run generate:font-regular && npm run generate:font-filled && npm run generate:font-light && npm run generate:font-resizable", "generate:rtl": "node ../../importer/rtlMetadata.js --source=../../assets --dest=./intermediate/rtl.json", "rollup": "node ./generateRollup.js", "optimize": "svgo --config svgo.config.js --folder=./intermediate --precision=2 --quiet", "unfill": "find ./intermediate -type f -name \"*.svg\" -exec sed -i.bak 's/fill=\"none\"//g' {} \\; && find ./intermediate -type f -name \"*.bak\" -delete", "build": "npm run copy && npm run generate:font && npm run generate:rtl && npm run optimize && npm run unfill && npm run convert:svg && npm run convert:fonts && npm run clean:svg && npm run build:esm && npm run build:cjs && npm run copy:font-files", "build:svg": "npm run copy && npm run generate:rtl && npm run optimize && npm run unfill && npm run convert:svg && npm run clean:svg && npm run build:esm && npm run build:cjs", "build:cjs": "tsc --module commonjs --outDir lib-cjs && babel lib-cjs --out-dir lib-cjs", "build:esm": "tsc && babel lib --out-dir lib"}, "devDependencies": {"@babel/cli": "^7.16.0", "@babel/core": "^7.16.0", "@babel/preset-typescript": "^7.16.7", "@griffel/babel-preset": "^1.0.0", "@types/react": "^17.0.2", "cpy-cli": "^4.1.0", "glob": "^7.2.0", "lodash": "^4.17.21", "mkdirp": "^1.0.4", "react": "^17.0.1", "renamer": "^2.0.1", "svgo": "^2.8.0", "typescript": "~4.1.0", "yargs": "^14.0.0"}, "dependencies": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <19.0.0"}, "files": ["lib/", "lib-cjs/"], "exports": {".": {"fluentIconFont": {"types": "./lib/fonts/index.d.ts", "import": "./lib/fonts/index.js", "require": "./lib-cjs/fonts/index.js"}, "default": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib-cjs/index.js"}}, "./fonts": {"types": "./lib/fonts/index.d.ts", "import": "./lib/fonts/index.js", "require": "./lib-cjs/fonts/index.js"}, "./lib/fonts": {"types": "./lib/fonts/index.d.ts", "import": "./lib/fonts/index.js", "require": "./lib-cjs/fonts/index.js"}, "./svg": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib-cjs/index.js"}, "./lib/svg": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib-cjs/index.js"}, "./providers": {"types": "./lib/providers.d.ts", "import": "./lib/providers.js", "require": "./lib-cjs/providers.js"}, "./lib/providers": {"types": "./lib/providers.d.ts", "import": "./lib/providers.js", "require": "./lib-cjs/providers.js"}}}