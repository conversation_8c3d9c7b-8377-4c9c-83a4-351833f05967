// Top-level build file where you can add configuration options common to all sub-projects/modules.
apply plugin: 'io.github.gradle-nexus.publish-plugin'

buildscript {
    ext.kotlin_version = '1.3.71'
    ext.appCenterSdkVersion = '2.5.1'
    ext.nexus_staging_plugin_version = '1.1.0'
    repositories {
        maven { url "https://plugins.gradle.org/m2/" }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:4.2.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "io.github.gradle-nexus:publish-plugin:$nexus_staging_plugin_version"
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
project.ext {
    universalPkgDir = "universal" // Name of the directory to store prepared package
}

task makeSystemIconsUniversalPkg(type: Copy) {
    description = "Prepares the fluentui-system-icons AAR from the library module for universal packaging."
    duplicatesStrategy = DuplicatesStrategy.WARN

    // The AAR is generated in your 'library' module
    // From android/build.gradle, the path to the library's AAR output is 'library/build/outputs/aar/'
    from("library/build/outputs/aar/") {
        include "library-release.aar" // This assumes your AAR is named library-release.aar.
                                     // If it has a different name (e.g., based on an artifactId), adjust this.
                                     // For example, if artifactId is 'fluentui-system-icons', it might be 'fluentui-system-icons-release.aar'.
    }

    // The output directory will be 'android/build/universal/libs'
    // $buildDir here refers to 'fluentui-system-icons/android/build/'
    into("$buildDir/${project.ext.universalPkgDir}/libs")

    includeEmptyDirs = false

    // Rename the AAR to a simpler name if desired, e.g., remove '-release'.
    // This example renames 'library-release.aar' to 'library.aar'.
    // Adapt if your AAR is named differently or if you want a different target name.
    // If your AAR is 'fluentui-system-icons-release.aar' and you want 'fluentui-system-icons.aar':
    // rename 'fluentui-system-icons-release.aar', 'fluentui-system-icons.aar'
    // Or, more generically like fluentui-android:
    rename '(.+)-release', '$1'
}

apply from: "${rootDir}/scripts/publish-root.gradle"
